FRAGMENTS OF SANDRA - INTERACTIVE COMIC FRAMEWORK
=================================================

PROJECT STRUCTURE:
------------------
null-vesper/Sandra/
├── index.html          (Main HTML structure)
├── style.css           (Cyberpunk styling + glitch effects)
├── comic.js            (Comic engine with embedded data)
├── img/                (Comic panel images)
│   ├── panel1a.jpg     (Reality A variants)
│   ├── panel1b.jpg     (Reality B variants)
│   └── ...             (23 total images)
├── docs/               (Documentation)
│   ├── storyline.md    (Original story outline)
│   ├── exercise1.md    (Project requirements)
│   └── imageprompts.md (AI image generation prompts)
├── create_placeholders.html (Utility for generating test images)
└── framework.txt       (This file)

SYSTEM ARCHITECTURE:
===================

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   index.html    │    │    style.css    │    │    comic.js     │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │Comic Header │ │    │ │Cyberpunk    │ │    │ │ComicEngine  │ │
│ │Panel Area   │ │◄───┤ │Theme        │ │◄───┤ │Class        │ │
│ │Navigation   │ │    │ │Glitch FX    │ │    │ │State Mgmt   │ │
│ │UI Elements  │ │    │ │Responsive   │ │    │ │Navigation   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ │COMIC_DATA   │ │
└─────────────────┘    └─────────────────┘    │ │(embedded)   │ │
         │                       │            │ └─────────────┘ │
         │                       │            └─────────────────┘
         └───────────────────────┼───────────────────────┘
                                 │
                                 ▼
                    ┌─────────────────┐
                    │   img/ folder   │
                    │                 │
                    │ ┌─────────────┐ │
                    │ │Panel Images │ │
                    │ │23 JPG files │ │
                    │ │Cyberpunk Art│ │
                    │ └─────────────┘ │
                    └─────────────────┘

DATA FLOW:
==========

1. PAGE LOAD:
   index.html → comic.js → ComicEngine.init()

2. INITIALIZATION:
   ComicEngine uses embedded COMIC_DATA → initializeStoryState()

3. PANEL RENDERING:
   renderCurrentPanel() → COMIC_DATA.panels[panelIndex] → storyState[variantIndex] → DOM update

4. NAVIGATION:
   User clicks Next/Previous → goToNext()/goToPrevious() → triggerGlitchTransition() → renderCurrentPanel()

5. BRANCHING LOGIC:
   goToPrevious() → randomizeFromCurrentPanel() → updates storyState[] → creates alternate storylines

KEY FEATURES:
=============

BRANCHING STORYLINES:
- Each panel has A/B variants (Reality A: car accident, Reality B: overdose)
- Going backward re-randomizes current and future panels
- Creates infinite story variations

GLITCH EFFECTS:
- CSS animations for panel transitions
- Glitch overlay during navigation
- Cyberpunk visual aesthetic

STATE MANAGEMENT:
- storyState[] array tracks current variant per panel
- currentPanelIndex tracks position
- Navigation buttons update based on position

RESPONSIVE DESIGN:
- Mobile-friendly layout
- Keyboard navigation (arrow keys)
- Error handling for missing images

TECHNICAL IMPLEMENTATION:
========================

COMIC ENGINE CLASS METHODS:
- init(): Initialize comic system
- initializeStoryState(): Set random variants
- renderCurrentPanel(): Display current panel
- goToNext()/goToPrevious(): Navigation logic
- triggerGlitchTransition(): Visual effects
- randomizeFromCurrentPanel(): Branching logic

CSS FEATURES:
- CSS Grid/Flexbox layout
- Keyframe animations for glitch effects
- CSS custom properties for theming
- Media queries for responsiveness

EMBEDDED DATA STRUCTURE:
- COMIC_DATA.panels[]: Array of panel objects
- variants[]: A/B reality versions per panel
- Dialogue, captions, image references
- Speaker attribution and reality markers
- No external file dependencies

USAGE INSTRUCTIONS:
==================

1. Open index.html in web browser
2. Use Next/Previous buttons or arrow keys to navigate
3. Going backward triggers reality glitches and alternate storylines
4. Panel 12 is the convergence point (shared ending)
5. Reality indicator shows current timeline (A/B/Convergence)

CUSTOMIZATION:
==============

To modify the comic:
1. Edit COMIC_DATA object in comic.js to change dialogue/story
2. Replace images in img/ folder
3. Modify CSS variables in style.css for different themes
4. Extend ComicEngine class for additional features

BROWSER COMPATIBILITY:
=====================

- Modern browsers with ES6+ support
- Local file:// protocol compatible
- No external dependencies or CORS issues
- Responsive design for mobile/desktop
