<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fragments of Sandra - Interactive Comic</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="comic-container">
        <header class="comic-header">
            <h1 class="comic-title">Fragments of <PERSON></h1>
            <p class="comic-subtitle">A Glitching Reality</p>
        </header>
        
        <main class="comic-main">
            <div class="panel-container" id="panelContainer">
                <!-- Comic panels will be dynamically loaded here -->
            </div>
            
            <div class="panel-info">
                <span id="panelCounter">Panel 1 of 12</span>
                <span id="realityIndicator">Reality: ?</span>
            </div>
        </main>
        
        <nav class="comic-navigation">
            <button id="prevBtn" class="nav-btn prev-btn" disabled>
                <span class="btn-text">Previous</span>
                <span class="btn-subtext">Glitch Reality</span>
            </button>
            
            <button id="nextBtn" class="nav-btn next-btn">
                <span class="btn-text">Next</span>
                <span class="btn-subtext">Continue</span>
            </button>
        </nav>
        
        <div class="story-state">
            <p class="state-text">Navigate backward to experience alternate realities</p>
        </div>

        <footer class="comic-footer">
            <p class="footer-text">
                Created by <a href="https://woodmurderedhat.com" target="_blank" rel="noopener noreferrer">woodmurderedhat</a> |
                Published by <a href="https://420360.xyz" target="_blank" rel="noopener noreferrer">420360</a>
            </p>
        </footer>
    </div>

    <!-- Glitch overlay for transitions -->
    <div class="glitch-overlay" id="glitchOverlay"></div>
    
    <script src="comic.js"></script>
</body>
</html>
